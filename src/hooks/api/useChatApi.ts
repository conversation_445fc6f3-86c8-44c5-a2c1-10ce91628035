import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  type SendMessagePayload,
  MUTATION_KEYS,
  QUERY_KEYS,
} from '../../types/api';
import chatService from '../../services/chatService';
import { useChatStore } from '../../store';

// Note: ConversationData type available if needed for future use

// Hook for sending messages
export const useSendMessage = () => {
  const queryClient = useQueryClient();
  const { addMessage, updateMessage } = useChatStore();
  
  return useMutation({
    mutationKey: [MUTATION_KEYS.SEND_MESSAGE],
    mutationFn: async (payload: SendMessagePayload) => {
      return await chatService.sendMessage(payload);
    },
    onMutate: async (payload) => {
      // Create optimistic user message
      const tempMessage = {
        id: `temp_${Date.now()}`,
        content: payload.message,
        sender: 'user' as const,
        status: 'sending' as const,
        timestamp: new Date(),
      };
      
      // Add to store optimistically
      addMessage(tempMessage);
      
      // Cancel any outgoing refetches
      const conversationId = payload.conversationId || 'default';
      await queryClient.cancelQueries({ queryKey: QUERY_KEYS.MESSAGES(conversationId) });
      
      // Snapshot the previous value
      const previousMessages = queryClient.getQueryData(QUERY_KEYS.MESSAGES(conversationId));
      
      // Optimistically update the cache
      
      return { previousMessages, tempMessage };
    },
    onSuccess: (data, payload, context) => {
      // Update the temporary message to sent status
      if (context?.tempMessage) {
        updateMessage(context.tempMessage.id, {
          status: 'sent',
        });
      }
      
      // Add the AI response
      addMessage({
        content: data.response,
        sender: 'agent',
        status: 'sent',
      });
      
      // Invalidate and refetch messages
      const conversationId = payload.conversationId || 'default';
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.MESSAGES(conversationId) });
    },
    onError: (error, _payload, context) => {
      // Update the temporary message to error status
      if (context?.tempMessage) {
        updateMessage(context.tempMessage.id, {
          status: 'error',
          error: error.message,
        });
      }
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['messages'] });
    },
  });
};

// Utility hook for chat operations
export const useChatOperations = () => {
  const sendMessage = useSendMessage();
  
  return {
    sendMessage: sendMessage.mutate,
    sendMessageAsync: sendMessage.mutateAsync,
    // Loading states
    isSendingMessage: sendMessage.isPending,
    // Error states
    sendMessageError: sendMessage.error,
  };
};
