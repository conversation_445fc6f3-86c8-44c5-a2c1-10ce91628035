import {
  type ChatService,
  type SendMessagePayload,
  type SendMessageApiResponse,
} from '../types/api';
import { apiClientWithRetry } from '../utils/api';

// Chat API endpoints - simplified for basic message sending
const ENDPOINTS = {
  SEND_MESSAGE: '/chat',
} as const;

// Implementation of ChatService
class ChatServiceImpl implements ChatService {
  async sendMessage(payload: SendMessagePayload): Promise<SendMessageApiResponse> {
    const response = await apiClientWithRetry.post<SendMessageApiResponse>(
      ENDPOINTS.SEND_MESSAGE,
      payload,
      {},
      {
        retries: 2, // Fewer retries for message sending
        retryDelay: 500,
      }
    );
    
    return response.data;
  }
}

// Export singleton instance
export const chatService = new ChatServiceImpl();

// Mock service for development/testing
class MockChatService implements ChatService {
  
  async sendMessage(payload: SendMessagePayload): Promise<SendMessageApiResponse> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));
    
    const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const conversationId = payload.conversationId || 'default_conversation';
    
    // Simulate AI response - DISABLED (using real API now)
    // const responses = [
    //   "Hello! How can I help you today?",
    //   "That's an interesting question. Let me think about that...",
    //   "I understand what you're asking. Here's what I think:",
    //   "Thanks for sharing that with me. I'd be happy to help.",
    //   "That's a great point. Let me provide some more information:",
    //   "I see what you mean. Here's my perspective on that:",
    // ];

    const response = "Mock service is disabled. Using real API endpoint.";
    
    return {
      messageId,
      response,
      conversationId,
      timestamp: new Date().toISOString(),
      metadata: {
        processingTime: Math.random() * 2000,
        confidence: 0.8 + Math.random() * 0.2,
      },
    };
  }


}

// Export mock service for development
export const mockChatService = new MockChatService();

// Service factory
export const createChatService = (useMock = false): ChatService => {
  // Only use mock if explicitly requested via environment variable
  if (useMock || import.meta.env.VITE_USE_MOCK_API === 'true') {
    return mockChatService;
  }
  return chatService;
};

// Default export - use real service since VITE_USE_MOCK_API=false
export default createChatService(import.meta.env.VITE_USE_MOCK_API === 'true');
